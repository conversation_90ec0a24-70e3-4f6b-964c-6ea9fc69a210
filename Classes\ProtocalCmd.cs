using System.Text;
using System.IO;
using System.IO.Ports;

namespace CCTS
{
    public class ProtocalCmdItem
    {
        public byte[] cmd;
        public int lenCmd = 0;

        public byte[] recv;
        public int lenRecv = 0;
        internal int lenRecvReal = 0;
        internal bool recvValid;
        // public ProtocalCmdItem(byte[] cmdBytes, bool needTrans, byte target)
        // {
        //     if (needTrans)
        //     {
        //         lenCmd = cmdBytes.Length + 4;
        //         cmd = new byte[lenCmd];
        //         cmd[0] = 0xCC;              // 帧头标识
        //         cmd[1] = (byte)(cmdBytes.Length - 2);  // 修正：原始数据长度(不含帧头)

        //         // 插入透明传输控制字节
        //         cmd[2] = 0xAB;
        //         cmd[3] = target;

        //         // 复制有效载荷数据
        //         Array.Copy(cmdBytes, 2, cmd, 4, cmdBytes.Length - 2);
        //     }
        //     else
        //     {
        //         lenCmd = cmdBytes.Length + 2;
        //         cmd = new byte[lenCmd];
        //         Array.Copy(cmdBytes, 0, cmd, 0, cmdBytes.Length);
        //     }

        //     build();  // 构建完整协议包
        // }

        // public void build()
        // {           
        //         short crc = ProtocalCmd.cheecksum(cmd, lenCmd - 2, false);
        //         cmd[lenCmd - 2] = (byte)(crc & 0xFF);
        //         cmd[lenCmd - 1] = (byte)((crc >> 8) & 0xFF);
            
        // }


        public ProtocalCmdItem(byte[] cmdBytes, bool needTrans, byte target)
        {
            // lenCmd = cmdBytes.Length + 2;
            // cmd = new byte[lenCmd];

            // // 复制原始指令内容
            // Array.Copy(cmdBytes, 0, cmd, 0, cmdBytes.Length);
            if (needTrans)
            {

                lenCmd = cmdBytes.Length + 4; // 减去原CC和LEN + AB/target + CRC
                cmd = new byte[lenCmd];

                // 构建新指令帧头
                cmd[0] = 0xCC; // 帧头              
                cmd[1] = (byte)cmdBytes.Length;  // 原始数据长度 + AB/Target
                // 插入透明传输标识和目标地址
                cmd[2] = 0xAB;
                cmd[3] = target;

                // 复制原始命令主体（跳过前两字节的CC和LEN）
                Array.Copy(cmdBytes, 2, cmd, 4, cmdBytes.Length - 2);
            }
            else
            {
                // 非透明传输模式：直接复制原始指令
                lenCmd = cmdBytes.Length + 2; // 原始长度 + CRC
                cmd = new byte[lenCmd];
                Array.Copy(cmdBytes, 0, cmd, 0, cmdBytes.Length);
            }

            // 计算并添加校验和
            build();
        }
        public void build()
        {

            short crc = ProtocalCmd.cheecksum(cmd, lenCmd, false);
            // 将 存入最后两个字节（小端模式）
            cmd[lenCmd - 2] = (byte)(crc & 0xFF);       // 低字节在前
            cmd[lenCmd - 1] = (byte)((crc >> 8) & 0xFF); // 高字节在后

        }
    }
    /// <summary>
    /// 数据发送和接收验证
    /// </summary>
    public class ProtocalCmd
    {
        public static MainForm mainForm;
        public static readonly object _lock = new object();
        public static bool bOldViesion, bLink;
        public static string? strHardVerMaster, strSoftVerMaster;
        public static string dataFilesPath;
        public static float val;
        public static int idx;
        public static List<float> valueAll = new List<float>();
        public static bool writeLog = false;
        public static void commBlock(ProtocalCmdItem cmdMonite, bool bRecvAll = false, int iTimeout = -1)
        {
            lock (_lock)
            {
                if (!mainForm.serialPort.IsOpen)
                {                 
                    mainForm.serialPort.Open();
                    if (!mainForm.serialPort.IsOpen)
                    {
                        MainForm.Logger.Info("连接失败，稍后重连");
                        //Thread.Sleep(500);
                        return;
                    }
                }
                bool success = false;
                int attempt = 0;
                //iTimeout < SysConfig.iReadTimeout &&
                while (!success && attempt < 3)
                {
                    mainForm.serialPort.Write(cmdMonite.cmd, 0, cmdMonite.lenCmd);
                    MainForm.Logger.Info("--发送：" + BitConverter.ToString(cmdMonite.cmd, 0, cmdMonite.lenCmd).Replace("-", " "));
                    MainForm.Logger.Info($"--发送[尝试{attempt + 1}]:" +
                                 BitConverter.ToString(cmdMonite.cmd, 0, cmdMonite.lenCmd).Replace("-", " "));
                    Thread.Sleep(20);

                    int effectiveTimeout = iTimeout > 0 ? iTimeout : SysConfig.iReadTimeout;
                    cmdMonite.recvValid = CommRecv(cmdMonite, effectiveTimeout);
                    if (!cmdMonite.recvValid)
                    {
                        MainForm.Logger.Info($"接收失败[尝试{attempt + 1}],{50}ms后重试...");
                        success = false;
                        attempt++;
                    }
                    else
                    {
                        success = true;
                        MainForm.Logger.Info($"接收结果: {cmdMonite.recvValid}");
                    }
                    if (writeLog)
                    {
                        cmdMonite.lenRecvReal = cmdMonite.recv.Length;
                        recordData(cmdMonite);
                    }

                    // 记录接收结果状态
                    MainForm.Logger.Info($"接收结果: {cmdMonite.recvValid}");
                }

            }
        }
        //接收数据
        public static bool CommRecv(ProtocalCmdItem cmdMonite, int iTimeout = -1)
        {
            DateTimeOffset t0 = DateTimeOffset.UtcNow;
            List<byte> packetBuffer = new List<byte>();
            bool res = false;
            bool iStart = false;
            byte[] buffer = new byte[4096];
            int len = 0;
            
            try
            {
                while ((DateTimeOffset.UtcNow - t0).TotalMilliseconds < SysConfig.iReadTimeout)
                {
                    if (mainForm.serialPort.BytesToRead == 0)
                        continue;

                    // 一次性读取所有可用数据
                    int bytesRead = mainForm.serialPort.Read(buffer, 0, Math.Min(buffer.Length, mainForm.serialPort.BytesToRead));

                    for (int i = 0; i < bytesRead; i++)
                    {
                        byte currentByte = buffer[i];

                        // 检测到新的起始符：重置缓冲区
                        if (currentByte == 0xCC)
                        {
                            packetBuffer.Clear();
                            packetBuffer.Add(0xCC);
                            iStart = true;
                            continue;
                        }

                        // 添加数据到缓冲区
                        packetBuffer.Add(currentByte);

                        // 当缓冲区有足够数据解析长度时
                        if (packetBuffer.Count >= 2)
                        {
                            int contentLen = packetBuffer[1];  // 第二字节是数据长度
                            len = contentLen + 4;    // 完整包长度
                        }

                        // 检查是否收到完整包
                        if (iStart && packetBuffer.Count >= len)
                        {
                            // 提取完整数据包（包含头尾）
                            byte[] fullPacket = packetBuffer.Take(len).ToArray();
                            MainForm.Logger.Info($"++接收(未验证): {BitConverter.ToString(fullPacket).Replace("-", " ")}");

                            // 验证数据包
                            res = ValidateData(fullPacket, fullPacket.Length);

                            if (res)
                            {
                                cmdMonite.recv = fullPacket;
                                MainForm.Logger.Info($"++接收(验证后): {BitConverter.ToString(cmdMonite.recv).Replace("-", " ")}");
                                linkCom(cmdMonite);
                                dislinkCom(cmdMonite);
                                // if (writeLog)
                                //     recordData(cmdMonite);                                
                            }
                            MainForm.Logger.Info($"接收验证结果: {res}");


                            // 清除已处理数据
                            packetBuffer.RemoveRange(0, len);
                            return res;
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                MainForm.Logger.Error($"接收错误: {ex.Message}\n{ex.StackTrace}");
                res = false;
            }

            // 超时或未收到完整包
            return res;
        }
        ///
        ///主界面芯片连接与断开
        //连接  
        public static void linkCom(ProtocalCmdItem cmdMonite)
        {
            bool res = true;
            try
            {

                // 重新设计验证逻辑
                if (cmdMonite.recv.Length == 0)
                {
                    res = false;
                }
                else
                {
                    // 预设的标准响应模板(新版本)
                    byte[] cmdRecv = { 0xcc, 0x06, 0xf0, 0x01, 0xfe, 0x00, 0x01, 0x01 };
                    bool isFullMatch = false;

                    //检查是否与cmdRecv完全匹配
                    for (int i = 0; i < cmdRecv.Length; i++)
                    {
                        if (cmdRecv[i] == cmdMonite.recv[i])
                        {
                            isFullMatch = true;
                            break;
                        }
                        else
                            break;
                    }

                    if (isFullMatch)
                    {
                        //完全匹配
                        bOldViesion = false;
                        res = true;
                    }
                    else
                    {
                        //检查特殊响应模式(老版本)
                        if (cmdMonite.recv[1] == 0x04 && cmdMonite.recv[5] == 0x00)
                        {
                            bOldViesion = true;
                            res = true;
                        }
                        else
                        {
                            res = false;
                        }
                    }
                }

                if (res)
                    bLink = true;
            }
            catch (Exception ex)
            {
                MainForm.Logger.Error(ex.Message);
                MainForm.Logger.Error(ex.StackTrace);
                res = false;
            }
            finally
            {
                bLink = res;
                MainForm.Logger.Info($"连接验证结果: {bLink}");
                MainForm.Logger.Info($"老版本结果: {bOldViesion}");
            }
            //return res;

        }
        //断开连接
        public static void dislinkCom(ProtocalCmdItem cmdMonite)
        {
            bool res = true;
            try
            {
                // 重新设计验证逻辑
                if (cmdMonite.recv.Length == 0)
                {
                    res = false;
                }
                else
                {
                    // 预设的标准响应模板
                    byte[] cmdRecv = { 0xcc, 0x06, 0xf0, 0x02, 0xfd, 0x00, 0x01, 0x01 };
                    bool isFullMatch = false;

                    //检查是否与cmdRecv完全匹配
                    for (int i = 0; i < cmdRecv.Length; i++)
                    {
                        if (i >= cmdMonite.recv.Length || cmdRecv[i] != cmdMonite.recv[i])
                        {
                            isFullMatch = false;
                            res = false;
                            break;
                        }
                    }

                    if (isFullMatch)
                    {
                        res = true;
                    }
                }
                if (res)
                {
                    bLink = false;
                    MainForm.Logger.Info($"断开连接验证结果: {bLink}");
                }

            }
            catch (Exception ex)
            {
                MainForm.Logger.Error(ex.Message);
                MainForm.Logger.Error(ex.StackTrace);
                res = false;
            }
        }
        //Configuration界面
        //读取软件版本、硬件版本。
        /*
            表单信息cmd：[0xf2,0x07,0xf8]
            软件版本cmd：[0xf2,0x08,0xf7]
            硬件版本cmd：[0xf2,0x09,0xf6]
        */
        //读设备信息
        public static bool ReadInfo(ProtocalCmdItem cmdMonite, byte[] cmdkey, byte[] resBuff, bool needTrans, int recLen, byte target)
        {
            bool res = true;
            string asciiResult = string.Empty;

            try
            {
                if (bLink)
                {
                    byte[] cmd = new byte[5] { 0xcc, (byte)(needTrans ? 0x05 : 0x03), cmdkey[0], cmdkey[1], cmdkey[2] };
                    cmdMonite = new ProtocalCmdItem(cmd, needTrans, target);

                    commBlock(cmdMonite);
                    Thread.Sleep(50);

                    if (cmdMonite.recv != null && cmdMonite.recvValid)
                    {
                        // if (cmdMonite.recv[0] != 0xcc || cmdMonite.recv[2] != cmdkey[0] || cmdMonite.recv[3] != cmdkey[1] || cmdMonite.recv[4] != cmdkey[3])
                        // {
                        //     res = false;
                        // }
                        if (cmdMonite.recv[0] == 0xcc || cmdMonite.recv[2] == cmdkey[0] || cmdMonite.recv[3] == cmdkey[1] || cmdMonite.recv[4] == cmdkey[3])
                        {
                            if (res)
                            {
                                recLen = cmdMonite.recv[1];  // 从第二个字节获取数据长度
                                int dataLength = recLen - 3;


                                if (dataLength > 0)
                                {
                                    // Array.Copy(cmdMonite.recv, 5, resBuff, 0, dataLength);
                                    // 跳过前5字节头，跳过最后2字节尾
                                    int startIndex = 5;
                                    int endIndex = dataLength;

                                    // 复制去除头尾的数据
                                    Array.Copy(cmdMonite.recv, startIndex, resBuff, 0, dataLength);

                                    // 将去除头尾的数据转换为ASCII
                                    asciiResult = Encoding.ASCII.GetString(resBuff, 0, dataLength);
                                }
                                res = true;
                                //软件版本号
                                if (cmdMonite.recv[2] == 0xf2 && cmdMonite.recv[3] == 0x06 && cmdMonite.recv[4] == 0xf9)
                                    strSoftVerMaster = asciiResult;
                                //硬件版本号
                                else if (cmdMonite.recv[2] == 0xf2 && cmdMonite.recv[3] == 0x07 && cmdMonite.recv[4] == 0xf8)
                                    strHardVerMaster = asciiResult;

                                //MainForm.Logger.Info($"控制器验证结果: {res}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MainForm.Logger.Error(ex.Message);
                MainForm.Logger.Error(ex.StackTrace);
                res = false;
            }
            MainForm.Logger.Info($"控制器验证结果: {res}");
            return res;

        }
        //读参数
        public static bool ReadParam(short index, bool needTrans, byte target)
        {
            lock (_lock)
            {
                bool res = true;
                try
                {
                    if (bLink)
                    {
                        byte[] indexBytes = BitConverter.GetBytes(index);
                        byte[] cmd = { 0xcc, (byte)(needTrans ? 0x07 : 0x05), 0xf2, 0x01, 0xfe, indexBytes[0], indexBytes[1] };
                        ProtocalCmdItem cmdMonite = new ProtocalCmdItem(cmd, needTrans, target);

                        commBlock(cmdMonite);
                        Thread.Sleep(50);

                        if (cmdMonite.recv != null && cmdMonite.recvValid)
                        {
                            // 检查错误码（位置11）
                            if (cmdMonite.recv[11] != 0)
                            {
                                return false;
                            }

                            // 验证包头（前5字节）
                            byte[] expectedHeader = { 0xCC, 0x0A, cmd[2], cmd[3], cmd[4] };
                            for (int i = 0; i < expectedHeader.Length; i++)
                            {
                                if (cmdMonite.recv[i] != expectedHeader[i])
                                {
                                    return false;
                                }
                            }

                            // 提取数据
                            idx = BitConverter.ToInt16(cmdMonite.recv, 5);
                            val = BitConverter.ToSingle(cmdMonite.recv, 7);

                        }
                    }


                }
                catch (Exception ex)
                {
                    MainForm.Logger.Error(ex.Message);
                    MainForm.Logger.Error(ex.StackTrace);
                    res = false;
                }
                MainForm.Logger.Info($"读取参数验证结果: {res}");
                return res;
            }

        }
        //写参数
        public static bool WriteParam(short index, float value, bool needTrans, byte target)
        {
            bool res = true;
            lock (_lock)
            {
                try
                {
                    if (bLink)
                    {
                        // 构造写参数指令
                        byte[] indexBytes = BitConverter.GetBytes(index);
                        byte[] valueBytes = BitConverter.GetBytes(value);

                        byte[] cmd = new byte[] { 0xCC, (byte)(needTrans ? 0x0B : 0x09), 0xF2, 0x02,0xFD,
                        indexBytes[0], indexBytes[1],
                         valueBytes[0], valueBytes[1], valueBytes[2], valueBytes[3]
                    };

                        ProtocalCmdItem cmdMonite = new ProtocalCmdItem(cmd, needTrans, target);

                        commBlock(cmdMonite);
                        Thread.Sleep(50); // 保持原始通信等待

                        if (cmdMonite.recv != null && cmdMonite.recvValid)
                        {
                            // 检查错误码（位置11）
                            if (cmdMonite.recv[11] == 0)
                            {
                                // 验证包头（前5字节）
                                byte[] expectedHeader = { 0xCC, 0x0A, cmd[2], cmd[3], cmd[4] };
                                for (int i = 0; i < expectedHeader.Length; i++)
                                {
                                    if (cmdMonite.recv[i] != expectedHeader[i])
                                    {
                                        return false;
                                    }
                                }
                            }

                            res = true;
                        }
                        else
                        {
                            res = false;
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainForm.Logger.Error(ex.Message);
                    MainForm.Logger.Error(ex.StackTrace);
                    res = false;
                }
            }

            MainForm.Logger.Info($"写入参数验证结果: {res}");
            return res;
        }
        ///
        /// Log界面
        /// 
        //监控
        public static bool ParamMoniting(bool needTrans, byte target)
        {
            lock (_lock)
            {
                bool res = true;
                try
                {
                    if (bLink)
                    {
                        byte[] cmd = { 0xcc, (byte)(needTrans ? 0x05 : 0x03), 0xF4, 0x01, 0xFE };
                        ProtocalCmdItem cmdMonite = new ProtocalCmdItem(cmd, needTrans, target);

                        commBlock(cmdMonite);
                        Thread.Sleep(50);

                        if (cmdMonite.recv != null && cmdMonite.recvValid)
                        {
                            MainForm.Logger.Info("开始验证接收的信息");
                            // 验证包头（前5字节）
                            byte[] expectedHeader = { 0xCC, 0x53, cmd[2], cmd[3], cmd[4] };
                            //byte[] expectedHeader = { 0xCC, 0x1f, cmd[2], cmd[3], cmd[4] };
                            for (int i = 0; i < expectedHeader.Length; i++)
                            {
                                if (cmdMonite.recv[i] != expectedHeader[i])
                                {
                                    return false;
                                }
                            }

                            MainForm.Logger.Info("验证通过");

                            //计算浮点数个数
                            int dataStartIndex = 5; // 跳过包头5字节
                            int dataLength = cmdMonite.recv.Length - dataStartIndex - 2;
                            int floatCount = dataLength / 4;  // 每4字节一个float

                            MainForm.Logger.Info("开始向valueAll内添加数据");

                            //遍历解析每个float
                            for (int i = 0; i < floatCount; i++)
                            {
                                // 计算当前float的起始位置
                                int byteOffset = dataStartIndex + i * 4;

                                // 提取4字节并转为float
                                val = BitConverter.ToSingle(cmdMonite.recv, byteOffset);
                                valueAll.Add(val);

                                // 打印当前是第几个浮点数（索引从0开始）
                                MainForm.Logger.Info($"浮点数 #{i} (位置: {byteOffset}-{byteOffset + 3}): {val}");
                                MainForm.Logger.Info($"接收数组{valueAll}");
                            }
                            MainForm.Logger.Info("valueAll内添加数据完成");
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainForm.Logger.Error(ex.Message);
                    MainForm.Logger.Error(ex.StackTrace);
                    res = false;
                }
                MainForm.Logger.Info($"读取参数验证结果: {res}");
                return res;
            }
        }
        //读取转速
        public static bool ReadComSpeed(bool needTrans, byte target)
        {
            lock (_lock)
            {
                bool res = true;
                try
                {
                    if (bLink)
                    {
                        //byte[] indexBytes = BitConverter.GetBytes(index);
                        byte[] cmd = { 0xcc, (byte)(needTrans ? 0x05 : 0x03), 0xf4, 0x07, 0xf8 };
                        ProtocalCmdItem cmdMonite = new ProtocalCmdItem(cmd, needTrans, target);

                        commBlock(cmdMonite);
                        Thread.Sleep(50);

                        if (cmdMonite.recv != null && cmdMonite.recvValid)
                        {
                            // 检查错误码（位置11）
                            if (cmdMonite.recv[9] != 0)
                            {
                                return false;
                            }

                            // 验证包头（前5字节）
                            byte[] expectedHeader = { 0xCC, 0x08, cmd[2], cmd[3], cmd[4] };
                            for (int i = 0; i < expectedHeader.Length; i++)
                            {
                                if (cmdMonite.recv[i] != expectedHeader[i])
                                {
                                    return false;
                                }
                            }

                            // 提取数据
                            //idx = BitConverter.ToInt16(cmdMonite.recv, 5);
                            val = BitConverter.ToSingle(cmdMonite.recv, 5);
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainForm.Logger.Error(ex.Message);
                    MainForm.Logger.Error(ex.StackTrace);
                    res = false;
                }
                MainForm.Logger.Info($"读取转速参数验证结果: {res}");
                return res;
            }

        }
        //写参数
        public static bool WriteComSpeed(float value, bool needTrans, byte target)
        {
            bool res = true;
            lock (_lock)
            {
                try
                {
                    if (bLink)
                    {
                        // 构造写参数指令
                        //byte[] indexBytes = BitConverter.GetBytes(index);
                        byte[] valueBytes = BitConverter.GetBytes(value);

                        byte[] cmd = new byte[] { 0xCC, (byte)(needTrans ? 0x0a : 0x08), 0xF4, 0x06, 0xF9, 0xd1,
                        //indexBytes[0], indexBytes[1],
                         valueBytes[0], valueBytes[1], valueBytes[2], valueBytes[3]
                    };

                        ProtocalCmdItem cmdMonite = new ProtocalCmdItem(cmd, needTrans, target);

                        commBlock(cmdMonite);
                        Thread.Sleep(50); // 保持原始通信等待

                        if (cmdMonite.recv != null && cmdMonite.recvValid)
                        {
                            // 检查错误码（位置11）
                            if (cmdMonite.recv[5] == 0)
                            {
                                // 验证包头（前5字节）
                                byte[] expectedHeader = { 0xCC, 0x04, cmd[2], cmd[3], cmd[4] };
                                for (int i = 0; i < expectedHeader.Length; i++)
                                {
                                    if (cmdMonite.recv[i] != expectedHeader[i])
                                    {
                                        return false;
                                    }
                                }
                            }
                            res = true;
                        }
                        else
                        {
                            res = false;
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainForm.Logger.Error(ex.Message);
                    MainForm.Logger.Error(ex.StackTrace);
                    res = false;
                }
            }

            MainForm.Logger.Info($"写入转速参数验证结果: {res}");
            return res;
        }
        //压缩机运转与停止
        public static bool RunorStop(bool needTrans, bool run, byte target)
        {
            lock (_lock)
            {
                bool res = true;
                try
                {
                    if (bLink)
                    {
                        //byte[] indexBytes = BitConverter.GetBytes(index);
                        byte[] cmd = { 0xcc, (byte)(needTrans ? 0x05 : 0x03), 0xf4, (byte)(run ? 0x04 : 0x05), (byte)(run ? 0xfb : 0xfa) };
                        ProtocalCmdItem cmdMonite = new ProtocalCmdItem(cmd, needTrans, target);

                        commBlock(cmdMonite);
                        Thread.Sleep(50);

                        if (cmdMonite.recv != null && cmdMonite.recvValid)
                        {
                            // 检查错误码（位置11）
                            if (cmdMonite.recv[5] != 0)
                            {
                                return false;
                            }

                            // 验证包头（前5字节）
                            byte[] expectedHeader = { 0xCC, 0x04, cmd[2], cmd[3], cmd[4] };
                            for (int i = 0; i < expectedHeader.Length; i++)
                            {
                                if (cmdMonite.recv[i] != expectedHeader[i])
                                {
                                    return false;
                                }
                            }

                            // 提取数据
                            //idx = BitConverter.ToInt16(cmdMonite.recv, 5);
                            //val = BitConverter.ToSingle(cmdMonite.recv, 5);
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainForm.Logger.Error(ex.Message);
                    MainForm.Logger.Error(ex.StackTrace);
                    res = false;
                }
                MainForm.Logger.Info($"压缩机运动状态验证结果: {res}");
                return res;
            }

        }
        //日志保存
        public static void creatFile()
        {
            if (SysConfig.strDataPath.Trim().Length == 0)
                SysConfig.strDataPath = "./data";
            DateTime curTime = DateTime.Now;
            string curPath = $"{SysConfig.strDataPath}/Datas_{curTime.ToString("yyyy-MM-dd-HH-mm-ss")}";
            try
            {
                // 确保目录存在
                if (!Directory.Exists(SysConfig.strDataPath))
                    Directory.CreateDirectory(SysConfig.strDataPath);

                dataFilesPath = string.Format("{0}/log_{1}_{2:yyyyMMddHHmmss}.csv", SysConfig.strDataPath, "COM", curTime);
                File.WriteAllText(dataFilesPath, "Time,Type,Data\n");
            }
            catch (Exception ex)
            {
                MainForm.Logger.Error($"文件创建失败：{ex.Message}");
                MainForm.Logger.Error($"文件创建失败{ex.StackTrace}");
            }
        }
        public static void recordData(ProtocalCmdItem cmdMonite)
        {
            MainForm.Logger.Info("开始进入日志记录");
            using (StreamWriter writer = new StreamWriter(dataFilesPath, true, new UTF8Encoding(true)))
            {
                MainForm.Logger.Info("已经进入日志记录");
                // 获取当前时间
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");

                // 发送数据记录
                string cmdHex = BitConverter.ToString(cmdMonite.cmd, 0, cmdMonite.lenCmd).Replace("-", " ");
                MainForm.Logger.Info("开始记录发送指令");
                writer.WriteLine($"{timestamp},To,{cmdHex}");
                MainForm.Logger.Info("记录发送指令完成");

                // 接收数据记录
                if (cmdMonite.recv != null && cmdMonite.lenRecvReal > 0)
                {
                    MainForm.Logger.Info("进入接收日志记录");
                    string recvTimestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                    string recvHex = BitConverter.ToString(cmdMonite.recv, 0, cmdMonite.lenRecvReal).Replace("-", " ");
                    writer.WriteLine($"{recvTimestamp},Fm,{recvHex}");
                    MainForm.Logger.Info("接收日志记录完成");
                }
            }
        }


        //功能协议*************************************************************************************
        //参数保存
        public static bool SaveParam(bool needTrans, byte target)
        {
            bool res = true;
            lock (_lock)
            {
                try
                {
                    if (bLink)
                    {
                        // 构造保存参数指令
                        byte[] cmd = new byte[] { 0xCC, (byte)(needTrans ? 0x05 : 0x03), 0xf2, 0x03, 0xfc };

                        ProtocalCmdItem cmdMonite = new ProtocalCmdItem(cmd, needTrans, target);

                        commBlock(cmdMonite);
                        Thread.Sleep(50); // 保持原始通信等待

                        if (cmdMonite.recv != null && cmdMonite.recvValid)
                        {
                            // 检查错误码
                            if (cmdMonite.recv[5] == 0)
                            {
                                // 验证包头（前5字节）
                                byte[] expectedHeader = { 0xCC, 0x04, cmd[2], cmd[3], cmd[4] };
                                for (int i = 0; i < expectedHeader.Length; i++)
                                {
                                    if (cmdMonite.recv[i] != expectedHeader[i])
                                    {
                                        return false;
                                    }
                                }
                            }
                            res = true;
                        }
                        else
                        {
                            res = false;
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainForm.Logger.Error(ex.Message);
                    MainForm.Logger.Error(ex.StackTrace);
                    res = false;
                }
            }

            MainForm.Logger.Info($"写入转速参数验证结果: {res}");
            return res;
        }

        //计算校验码
        // public static ushort cheecksum(byte[] datas, int len, bool needTrans)
        // {
        //     // 初始化 CRC 为 0xFFFF
        //     ushort crc = 0xFFFF;

        //     // 遍历数据（不包括最后两个 CRC 预留位）
        //     for (int i = 0; i < len - 2; i++)
        //     {
        //         //当前字节与 CRC 低字节异或
        //         crc ^= datas[i];

        //         //处理 8 个比特位
        //         for (int j = 0; j < 8; j++)
        //         {
        //             bool lsbIsOne = (crc & 0x0001) == 1; // 检查最低位
        //             crc >>= 1;  // 右移一位

        //             if (lsbIsOne)
        //                 crc ^= 0xA001;  // 多项式反转值
        //         }
        //     }

        //     // // 将 CRC 存入最后两个字节（小端模式）
        //     // datas[len - 2] = (byte)(crc & 0xFF);       // 低字节在前
        //     // datas[len - 1] = (byte)((crc >> 8) & 0xFF); // 高字节在后
        //     return crc;
        // }
        public static short cheecksum(byte[] buff, int len,bool needTrans )
        {
            short nSum = 0;

            if (buff != null && len > 0)
            {         
                for (int idx = 0; idx < len; idx++)
                {
                    nSum += buff[idx];
                }
            }
            return nSum;
        }

        public static bool ValidateData(byte[] buff, int len)
        {
            // 检查基础条件
            if (buff == null || len < 2 || buff.Length < len)
                return false;

            // 计算校验和（排除最后2字节校验码）
            ushort crc = (ushort)cheecksum(buff, len-2, false);

            // 将short拆解为2字节（小端序：低字节在前）
            byte crcLow = (byte)(crc & 0xFF);       // 低字节
            byte crcHigh = (byte)((crc >> 8) & 0xFF); // 高字节            

            // 比较校验和（最后2字节是存储的校验值）
            return crcLow == buff[len - 2] && crcHigh == buff[len - 1];
        }
        
    }
}