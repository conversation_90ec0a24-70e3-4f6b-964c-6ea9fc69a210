{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x64": {"Tecumseh/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.1", "NLog": "5.5.0", "NLog.Extensions.Logging": "5.5.0", "System.Configuration.ConfigurationManager": "8.0.1", "System.IO.Ports": "9.0.6", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "8.0.18", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "8.0.18"}, "runtime": {"Tecumseh.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.18": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1825.31117"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.42.34436.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "8.0.1825.31117"}, "clretwrc.dll": {"fileVersion": "8.0.1825.31117"}, "clrgc.dll": {"fileVersion": "8.0.1825.31117"}, "clrjit.dll": {"fileVersion": "8.0.1825.31117"}, "coreclr.dll": {"fileVersion": "8.0.1825.31117"}, "createdump.exe": {"fileVersion": "8.0.1825.31117"}, "hostfxr.dll": {"fileVersion": "8.0.1825.31117"}, "hostpolicy.dll": {"fileVersion": "8.0.1825.31117"}, "mscordaccore.dll": {"fileVersion": "8.0.1825.31117"}, "mscordaccore_amd64_amd64_8.0.1825.31117.dll": {"fileVersion": "8.0.1825.31117"}, "mscordbi.dll": {"fileVersion": "8.0.1825.31117"}, "mscorrc.dll": {"fileVersion": "8.0.1825.31117"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/8.0.18": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31203"}, "DirectWriteForwarder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31203"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1825.31203"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "PresentationCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "PresentationFramework-SystemCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "PresentationFramework-SystemData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "PresentationFramework-SystemDrawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "PresentationFramework-SystemXml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "PresentationFramework-SystemXmlLinq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "PresentationFramework.Aero.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "PresentationFramework.Aero2.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "PresentationFramework.AeroLite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "PresentationFramework.Classic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "PresentationFramework.Luna.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "PresentationFramework.Royale.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "PresentationFramework.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "PresentationUI.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "ReachFramework.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31203"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31203"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31203"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31203"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Printing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Windows.Controls.Ribbon.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31117"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31203"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31203"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31203"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31203"}, "System.Windows.Input.Manipulations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "System.Windows.Presentation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "System.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "UIAutomationClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "UIAutomationClientSideProviders.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "UIAutomationProvider.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "UIAutomationTypes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}, "WindowsFormsIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1825.31703"}}, "native": {"D3DCompiler_47_cor3.dll": {"fileVersion": "10.0.22621.3233"}, "PenImc_cor3.dll": {"fileVersion": "8.0.1825.31703"}, "PresentationNative_cor3.dll": {"fileVersion": "8.0.25.16802"}, "vcruntime140_cor3.dll": {"fileVersion": "14.44.34918.1"}, "wpfgfx_cor3.dll": {"fileVersion": "8.0.1825.31703"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}}}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "NLog/5.5.0": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.5.0.3962"}}}, "NLog.Extensions.Logging/5.5.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "NLog": "5.5.0"}, "runtime": {"lib/net8.0/NLog.Extensions.Logging.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.5.0.656"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.native.System.IO.Ports/9.0.6": {"dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.6"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.6": {}, "System.Configuration.ConfigurationManager/8.0.1": {"dependencies": {"System.Diagnostics.EventLog": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}}, "System.Diagnostics.EventLog/8.0.1": {}, "System.IO.Ports/9.0.6": {"dependencies": {"runtime.native.System.IO.Ports": "9.0.6"}, "runtime": {"runtimes/win/lib/net8.0/System.IO.Ports.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.Security.Cryptography.ProtectedData/8.0.0": {}}}, "libraries": {"Tecumseh/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.18": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/8.0.18": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-EJzSNO9oaAXnTdtdNO6npPRsIIeZCBSNmdQ091VDO7fBiOtJAAeEq6dtrVXIi3ZyjC5XRSAtVvF8SzcneRHqKQ==", "path": "microsoft.extensions.configuration.fileextensions/8.0.1", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-L89DLNuimOghjV3tLx0ArFDwVEJD6+uGB3BMCMX01kaLzXkaXHb2021xOMl2QOxUxbdePKUZsUY7n2UUkycjRg==", "path": "microsoft.extensions.configuration.json/8.0.1", "hashPath": "microsoft.extensions.configuration.json.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "NLog/5.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-FCH8s7GWlonH5JXV9/EpeNJ8pRZQMVZOSWX3JrHPU8rzdHJhS5+lUGGvJIUOtzkGV1clYBFR0WXOI5FnUwVCMA==", "path": "nlog/5.5.0", "hashPath": "nlog.5.5.0.nupkg.sha512"}, "NLog.Extensions.Logging/5.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-l8gxUThXCiV5Vcilz9SF2/mf39p9ccdm+V3zoadQIfnv5dMHiy6BH8PSF/9ptOMHfUXZI5qOlBTiO+YcoMY/4Q==", "path": "nlog.extensions.logging/5.5.0", "hashPath": "nlog.extensions.logging.5.5.0.nupkg.sha512"}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-S1Gu7KIiBM5Zfkve5iaVZUMPToGZ8fc13IMLNdyU20G9nq9LnKSN5e0xb/TFr4N6IqWkAxmTD4JkcWXdjdk33g==", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-B5GL3MQcQ4OI02Q3jXdiISS5N7ZI6LCHyDQTpfJTpzTdf4SDwTMMxrcpGaPSth6l7yVyAtJJbIhgdFDw3PmOhg==", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-7Aq/6BqdFCShOlL+f7ltFCQkwo/YFzZ+wOmK8ObpGfzhxWp2Mg7H4DuMoqd1pO+ikdfbOcDm7cfdMmsUwgbKkQ==", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LG0iGAvkdSWD3WT+oZcozJFZKeINQ160n68YfnwVgtmFpMR2O3GIfuMrf9WJjfnZJb6pbaNnLGqOTpXVJTJa2Q==", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-x86.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-RzAA2D77LRuYVTUXKG8wLRbbDF6BA+fHeBtsdar3ARj3cWmqscR3sb5QgROBKtDj1G2Idu3aj+5Bk3TYc+f4XA==", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-a9pVVDsO76cdoGN3CYEgxTE54C5udq9hfOjPKVxyfwOk1N12w18VKL2a15deezFMqjGggaVyK0cFBj9qG7pqWw==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Kb8CZNgH0aWd8Ks6jS1F286SmVJ4FmJjiUdrQTvHu1aN1cWpfwLZ1qOARvFI3lbXE/geOzBIHDNWmQjyOAeUlg==", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BCbWsWzSdv9ly9pR1JFY89an+8nc9yZtjAPPlcpH31UUP0AuI27rnrKzcZAuqFXlKy2M8EZVnjV0czSTFueqGA==", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-eapJ2k/eeMK7Q/Mbticy5rgSb+qI884W9Wk6UTDnEdRpd0BvKbhgM845QEmk3vrxT6B8cCr4A8pRseZBdmk4WA==", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-+3S2ksd6iFNeyAxtCZ2dGlxXNGQsOgIgGiecu34++UnUTY9KFhkg8T69hyjEMg4+dRQXEWrU4+vP4AI3s5GlMw==", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Fa+EbKuQ6W4EzdVRAA/6ffJ3C0eQ93D8bhmnFaVEHBkfDTKNUSZKhjLdYgubvMrSQlsQ8XLGw0Ld1UXMgGCj7w==", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-POgI6/WVHtpxbqvfFqVybZckRbgFVp3nE0fOBpIQdKiZ9C3MPKKibyFNEBK81ZlgmtTEpJP0jMvLSuEbA/p95g==", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VMJ5KlYwc2OUPEXmuMetGdwU2IKCDH0mYPz+7G0e2psKJ6Q4JVq9VIOK/nnFJ9z0nbw7Cxu5m7nwh8p/ZPr/eA==", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-kbPhYFOoSo7l71lZo4RFx51Vj6BbQAL3QFn4duULbrpV1GEQX5ZrmBSpdxigcvDMit1i/+wDTyMll9t56i/knQ==", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Q8gHRVpOlTQSEqKtimf7s3lr8OaYaxXcrXjtF78k+6RogQ0BpEHnUgeBZBoQ53qSiztBAzkF22uPOHq+/+goOA==", "path": "runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-a+KcHUMoNJG1Z9uvf28HDmT8jK5rm0ExTUdScsdP/ukU2KE7ah+vLxNbh4zCxzvGHsx+Z6bVpaWLjuSYNbqilQ==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-ogySJ/MVq9J/X1ugQOAA4dQfoJSUnZtIbPLr8t2tsaGkV7TBgWOnFInRXy1c20o79M6ARyus12UinDKsFaLkwA==", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "path": "system.configuration.configurationmanager/8.0.1", "hashPath": "system.configuration.configurationmanager.8.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-n1ZP7NM2Gkn/MgD8+eOT5MulMj6wfeQMNS2Pizvq5GHCZfjlFMXV2irQlQmJhwA2VABC57M0auudO89Iu2uRLg==", "path": "system.diagnostics.eventlog/8.0.1", "hashPath": "system.diagnostics.eventlog.8.0.1.nupkg.sha512"}, "System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-D1nZmsZfUfKQ9/AqiAEafmnYijUoJvXtl0RWZ2P+q/Wq3gXgEtp+NzKTpabw2s0aiuPAsdx8SujQY06W2X4ucQ==", "path": "system.io.ports/9.0.6", "hashPath": "system.io.ports.9.0.6.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"]}}