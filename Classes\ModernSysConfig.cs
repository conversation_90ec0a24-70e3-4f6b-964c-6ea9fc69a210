using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using System.IO;

namespace CCTS
{
    public static class ModernSysConfig
    {
        private static IConfiguration? _configuration;
        
        public static int iBps { get; set; } = 9600;
        public static int iParity { get; set; } = 2;
        public static int iDatabit { get; set; } = 8;
        public static int iStopbit { get; set; } = 1;
        public static string? strPort { get; set; } = null;
        public static string strDataPath { get; set; } = "./datas";
        public static string[] portNames = SerialPort.GetPortNames();
        public static int iReadTimeout { get; set; } = 2000;  //读超时
        public static int iMoniteInterval { get; set; } = 2000;  //监控周期间隔

        //控制器类型
        public static byte TRG_LINKED_MASTER = 0x10;//主控芯片
        public static byte TRG_LINKED_SLAVE = 0x01;//驱动芯片
        public static byte TRG_STRUCT_S1 = 0x01;//仅驱动芯片
        public static byte TRG_STRUCT_M1S1 = 0x11;//主控+1个驱动
        public static byte TRG_STRUCT_M1S2 = 0x12;//主控+2个驱动
        public static byte bTargetLinked, bTargetStruct = new byte();

        public static void loadParam()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            _configuration = builder.Build();

            // 加载配置值
            var appSettings = _configuration.GetSection("AppSettings");
            
            strDataPath = appSettings["dataPath"] ?? "./datas";
            if (!Directory.Exists(strDataPath))
                Directory.CreateDirectory(strDataPath);

            if (int.TryParse(appSettings["bps"], out int bps))
                iBps = bps;

            if (int.TryParse(appSettings["parity"], out int parity))
                iParity = parity;

            if (int.TryParse(appSettings["databit"], out int databit))
                iDatabit = databit;

            if (int.TryParse(appSettings["stopbit"], out int stopbit))
                iStopbit = stopbit;

            strPort = appSettings["comm"];

            if (int.TryParse(appSettings["moniteInterval"], out int moniteInterval))
                iMoniteInterval = moniteInterval;
        }

        //保存串口设置
        public static void saveComm()
        {
            saveSettings();
        }

        public static void saveDataSetting()
        {
            saveSettings();
        }

        private static void saveSettings()
        {
            var settingsPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
            
            var settings = new
            {
                AppSettings = new
                {
                    dataPath = strDataPath,
                    bps = iBps,
                    parity = iParity,
                    databit = iDatabit,
                    stopbit = iStopbit,
                    comm = strPort,
                    moniteInterval = iMoniteInterval
                }
            };

            var json = System.Text.Json.JsonSerializer.Serialize(settings, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            });

            File.WriteAllText(settingsPath, json);
        }
    }
}
