﻿using System.Windows.Forms.VisualStyles;

namespace CCTS;

partial class PopFormTAL

{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }


    #region Windows Form Designer generated code
    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// 2025-06-05
    /// </summary>
    private void InitializeComponent()
    {

        this.components = new System.ComponentModel.Container();
        this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
        this.ClientSize = new System.Drawing.Size(2000, 1000);
        this.Text = "TAL Configuration";
         this.Icon = new System.Drawing.Icon("Resources/CCTS.ico");
        this.StartPosition = FormStartPosition.CenterScreen;
        this.BackColor = Color.FromArgb(78, 149, 217);
        this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.PopFormTAL_FormClosed);
        //
        //lable1
        //
        this.label1 = new Label();
        this.label1.Text = "Tecumseh Adaptive Logic\nCONFIGURATION";
        this.label1.Size = new Size(800, 200);
        this.label1.Anchor = AnchorStyles.None;
        this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
        this.label1.Location = new Point((this.ClientSize.Width - label1.Width) / 2, 60);
        this.label1.Font = new Font(label1.Font.FontFamily, 24, FontStyle.Bold);
        this.label1.ForeColor = Color.White;
        this.Controls.Add(label1);
        //
        //panel_talStep
        //
        this.panel_talStep = new CCTS.RoundcornerPanel();
        this.panel_talStep.Size = new Size(700, 500);
        this.panel_talStep.BackColor = Color.FromArgb(11, 48, 65);
        this.panel_talStep.ForeColor = Color.White;
        this.panel_talStep.Location = new Point(80, label1.Height + 80);
        this.Controls.Add(this.panel_talStep);
        //
        //label2
        //
        this.label2 = new Label();
        this.label2.Size = new Size(410, 60);
        this.label2.Text = "TAL FIRST POWER ON STEP";
        this.label2.Location = new Point((this.panel_talStep.Width - this.label2.Width) / 2, 20);
        this.label2.ForeColor = Color.White;
        this.label2.Font = new Font(label2.Font.FontFamily, 12, FontStyle.Bold);
        this.panel_talStep.Controls.Add(this.label2);
        //
        //btn_writeStep
        //
        this.btn_writeStep = new CCTS.Roundcornerbtn();
        this.btn_writeStep.Text = "Write";
        this.btn_writeStep.Size = new Size(160, 80);
        this.btn_writeStep.Location = new Point(160, 380);
        this.btn_writeStep.Font = new Font(btn_writeStep.Font.FontFamily, 12);
        this.btn_writeStep.BackColor = Color.FromArgb(47, 126, 39);//绿色
        this.btn_writeStep.FlatStyle = FlatStyle.Flat;
        this.btn_writeStep.FlatAppearance.BorderSize = 0;
        this.btn_writeStep.Click += new System.EventHandler(this.btn_writeStep_Click);
        this.panel_talStep.Controls.Add(this.btn_writeStep);
        //
        //btn_readStep
        //
        this.btn_readStep = new CCTS.Roundcornerbtn();
        this.btn_readStep.Text = "Read";
        this.btn_readStep.Size = new Size(160, 80);
        this.btn_readStep.Location = new Point(this.btn_writeStep.Width + 250, 380);
        this.btn_readStep.Font = new Font(btn_readStep.Font.FontFamily, 12);
        this.btn_readStep.BackColor = Color.FromArgb(233, 113, 50);//黄色
        this.btn_readStep.FlatStyle = FlatStyle.Flat;
        this.btn_readStep.FlatAppearance.BorderSize = 0;
        this.btn_readStep.Click += new System.EventHandler(this.btn_readStep_Click);
        this.panel_talStep.Controls.Add(this.btn_readStep);
        //
        //panel_talParameters
        //
        this.panel_talParameters = new CCTS.RoundcornerPanel();
        this.panel_talParameters.Size = new Size(1050, 500);
        this.panel_talParameters.Location = new Point(panel_talStep.Width + 160, label1.Height + 80);
        this.panel_talParameters.BackColor = Color.FromArgb(11, 48, 65);
        this.panel_talParameters.ForeColor = Color.White;
        this.Controls.Add(this.panel_talParameters);
        //
        //label3
        //
        this.label3 = new Label();
        this.label3.Size = new Size(480, 60);
        this.label3.Text = "TAL ALGORITHM PARAMETERS";
        this.label3.Location = new Point((this.panel_talParameters.Width - this.label3.Width) / 2, 20);
        this.label3.ForeColor = Color.White;
        this.label3.Font = new Font(label3.Font.FontFamily, 12, FontStyle.Bold);
        this.panel_talParameters.Controls.Add(this.label3);
        //
        //btn_writeParameters
        //
        this.btn_writeParameters = new CCTS.Roundcornerbtn();
        this.btn_writeParameters.Text = "Write";
        this.btn_writeParameters.Size = new Size(160, 80);
        this.btn_writeParameters.Location = new Point(280, 380);
        this.btn_writeParameters.Font = new Font(btn_writeParameters.Font.FontFamily, 12);
        this.btn_writeParameters.BackColor = Color.FromArgb(47, 126, 39);//绿色
        this.btn_writeParameters.FlatStyle = FlatStyle.Flat;
        this.btn_writeParameters.FlatAppearance.BorderSize = 0;
        this.btn_writeParameters.Click += new System.EventHandler(this.btn_writeParameters_Click);
        this.panel_talParameters.Controls.Add(this.btn_writeParameters);
        //
        //btn_readParameters
        //
        this.btn_readParameters = new CCTS.Roundcornerbtn();
        this.btn_readParameters.Text = "Read";
        this.btn_readParameters.Size = new Size(160, 80);
        this.btn_readParameters.Location = new Point(this.btn_writeStep.Width + 440, 380);
        this.btn_readParameters.Font = new Font(btn_readStep.Font.FontFamily, 12);
        this.btn_readParameters.BackColor = Color.FromArgb(233, 113, 50);//黄色
        this.btn_readParameters.FlatStyle = FlatStyle.Flat;
        this.btn_readParameters.FlatAppearance.BorderSize = 0;
        this.btn_readParameters.Click += new System.EventHandler(this.btn_readParameters_Click);
        this.panel_talParameters.Controls.Add(this.btn_readParameters);
        //
        //btn_Home
        //
        this.btn_Home = new CCTS.Roundcornerbtn();
        this.btn_Home.Text = "Home";
        this.btn_Home.Size = new Size(150, 80);
        this.btn_Home.Location = new Point(80, 860);
        this.btn_Home.ForeColor = Color.White;
        this.btn_Home.Font = new Font(this.btn_Home.Font.FontFamily, 12);
        this.btn_Home.BackColor = Color.FromArgb(11, 48, 65);
        this.btn_Home.FlatStyle = FlatStyle.Flat;
        this.btn_Home.FlatAppearance.BorderSize = 0;
        this.btn_Home.Click += new EventHandler(this.btn_Home_Click);
        this.Controls.Add(this.btn_Home);
        //
        //btn_Help
        //
        this.btn_Help = new CCTS.Roundcornerbtn();
        this.btn_Help.Text = "Help";
        this.btn_Help.Size = new Size(150, 80);
        this.btn_Help.Location = new Point(1760, 860);
        this.btn_Help.ForeColor = Color.White;
        this.btn_Help.Font = new Font(this.btn_Home.Font.FontFamily, 12);
        this.btn_Help.BackColor = Color.FromArgb(11, 48, 65);
        this.btn_Help.FlatStyle = FlatStyle.Flat;
        this.btn_Help.FlatAppearance.BorderSize = 0;
        //this.btn_Help.Click += new EventHandler(this.btn_Help_Click);
        this.Controls.Add(this.btn_Help);


    }

    #endregion
    private System.Windows.Forms.Label label1, label2, label3;
    private System.Windows.Forms.Panel panel_talStep, panel_talParameters;
    private System.Windows.Forms.Button btn_writeStep, btn_writeParameters, btn_readStep, btn_readParameters, btn_Home, btn_Help;
}

